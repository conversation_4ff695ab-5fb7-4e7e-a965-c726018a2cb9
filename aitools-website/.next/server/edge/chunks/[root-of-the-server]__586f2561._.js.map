{"version": 3, "sources": [], "sections": [{"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/config.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server';\nimport { notFound } from 'next/navigation';\n\n// 支持的语言列表\nexport const locales = ['en', 'zh'] as const;\nexport type Locale = (typeof locales)[number];\n\n// 默认语言\nexport const defaultLocale: Locale = 'en';\n\n// 语言显示名称\nexport const localeNames: Record<Locale, string> = {\n  zh: '中文',\n  en: 'English',\n};\n\n// 验证语言是否有效\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale);\n}\n\n// Next-intl 配置\nexport default getRequestConfig(async ({ locale }) => {\n  // 验证传入的语言是否有效\n  if (!isValidLocale(locale)) {\n    notFound();\n  }\n\n  return {\n    messages: (await import(`./messages/${locale}.json`)).default,\n  };\n});\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAAA;;;AAGO,MAAM,UAAU;IAAC;IAAM;CAAK;AAI5B,MAAM,gBAAwB;AAG9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;AACN;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B;uCAGe,CAAA,GAAA,kQAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,cAAc;IACd,IAAI,CAAC,cAAc,SAAS;QAC1B,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,OAAO;QACL,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF"}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { locales, defaultLocale } from './i18n/config';\n\n// 自定义语言检测函数\nfunction detectLocale(request: NextRequest): string {\n  // 获取Accept-Language头\n  const acceptLanguage = request.headers.get('accept-language');\n\n  if (acceptLanguage) {\n    // 检查是否包含中文\n    const isChinesePreferred = acceptLanguage.includes('zh') ||\n                              acceptLanguage.includes('zh-CN') ||\n                              acceptLanguage.includes('zh-TW') ||\n                              acceptLanguage.includes('zh-HK');\n\n    if (isChinesePreferred) {\n      return 'zh';\n    }\n  }\n\n  // 默认返回英文\n  return 'en';\n}\n\nexport function middleware(request: NextRequest) {\n  const pathname = request.nextUrl.pathname;\n\n  // 检查路径是否已经包含语言前缀\n  const pathnameHasLocale = locales.some(\n    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`\n  );\n\n  // 如果路径已经包含语言前缀，直接继续\n  if (pathnameHasLocale) {\n    return NextResponse.next();\n  }\n\n  // 只有在根路径且没有语言前缀时，才根据浏览器语言重定向\n  if (pathname === '/') {\n    const detectedLocale = detectLocale(request);\n    return NextResponse.redirect(new URL(`/${detectedLocale}`, request.url));\n  }\n\n  // 对于其他没有语言前缀的路径，添加默认英文前缀\n  // 这里不再使用浏览器检测，因为用户可能已经在其他页面选择了语言\n  return NextResponse.redirect(new URL(`/en${pathname}`, request.url));\n}\n\nexport const config = {\n  // 匹配所有路径，除了以下路径：\n  // - API 路由 (/api)\n  // - 静态文件 (_next/static)\n  // - 图片文件 (_next/image)\n  // - favicon.ico\n  // - robots.txt\n  // - sitemap.xml\n  matcher: [\n    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)'\n  ]\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,YAAY;AACZ,SAAS,aAAa,OAAoB;IACxC,qBAAqB;IACrB,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAE3C,IAAI,gBAAgB;QAClB,WAAW;QACX,MAAM,qBAAqB,eAAe,QAAQ,CAAC,SACzB,eAAe,QAAQ,CAAC,YACxB,eAAe,QAAQ,CAAC,YACxB,eAAe,QAAQ,CAAC;QAElD,IAAI,oBAAoB;YACtB,OAAO;QACT;IACF;IAEA,SAAS;IACT,OAAO;AACT;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;IAEzC,iBAAiB;IACjB,MAAM,oBAAoB,6HAAA,CAAA,UAAO,CAAC,IAAI,CACpC,CAAC,SAAW,SAAS,UAAU,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,aAAa,CAAC,CAAC,EAAE,QAAQ;IAG7E,oBAAoB;IACpB,IAAI,mBAAmB;QACrB,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,6BAA6B;IAC7B,IAAI,aAAa,KAAK;QACpB,MAAM,iBAAiB,aAAa;QACpC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,gBAAgB,EAAE,QAAQ,GAAG;IACxE;IAEA,yBAAyB;IACzB,iCAAiC;IACjC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,GAAG;AACpE;AAEO,MAAM,SAAS;IACpB,iBAAiB;IACjB,kBAAkB;IAClB,wBAAwB;IACxB,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,SAAS;QACP;KACD;AACH"}}]}