{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hTHUHp0Vu7ZoD2lx0Bvr6NSbQpiOl2TvijxC6rE2KCg=", "__NEXT_PREVIEW_MODE_ID": "93387ac23195e343b5eaa094fcb90e49", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b6b1d0a3f2d182d3a9159a135bd6f235b5f99cf0819049cadad35d9a3595406d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a540c42a2c1ce0cb1b6c31469dcab875d163cf196b71d22312de5652456fc10f"}}}, "sortedMiddleware": ["/"], "functions": {}}