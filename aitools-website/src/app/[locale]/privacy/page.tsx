import React from 'react';
import { Metadata } from 'next';
import Layout from '@/components/Layout';
import { getBreadcrumbStructuredData } from '@/lib/seo/structuredData';
import { Shield, Eye, Lock, Users } from 'lucide-react';

// 生成静态metadata
export const metadata: Metadata = {
  title: '隐私政策 - AI工具导航',
  description: 'AI工具导航隐私政策详细说明我们如何收集、使用、保护和分享您的个人信息。我们承诺保护您的隐私安全。',
  keywords: '隐私政策,个人信息保护,数据安全,用户隐私,AI工具导航',
  authors: [{ name: 'AI工具导航团队' }],
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/privacy`,
    siteName: 'AI工具导航',
    title: '隐私政策 - AI工具导航',
    description: 'AI工具导航隐私政策详细说明我们如何收集、使用、保护和分享您的个人信息。',
    images: [
      {
        url: '/og-privacy.jpg',
        width: 1200,
        height: 630,
        alt: '隐私政策 - AI工具导航',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: '隐私政策 - AI工具导航',
    description: 'AI工具导航隐私政策详细说明我们如何收集、使用、保护和分享您的个人信息。',
    images: ['/og-privacy.jpg'],
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/privacy`,
  },
};

export default function PrivacyPage() {
  // 生成结构化数据
  const breadcrumbStructuredData = getBreadcrumbStructuredData([
    { name: '首页', url: '/' },
    { name: '隐私政策', url: '/privacy' }
  ]);

  return (
    <Layout>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbStructuredData)
        }}
      />

      {/* 面包屑导航 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav className="flex" aria-label="面包屑导航">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <a
                href="/"
                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
              >
                首页
              </a>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">隐私政策</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="bg-blue-100 rounded-full p-4">
              <Shield className="h-12 w-12 text-blue-600" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            隐私政策
          </h1>
          <p className="text-xl text-gray-600">
            我们承诺保护您的隐私，本政策详细说明我们如何收集、使用和保护您的个人信息。
          </p>
          <p className="text-sm text-gray-500 mt-4">
            最后更新时间：2024年6月28日
          </p>
        </div>

        {/* Privacy Principles */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <div className="text-center p-6 bg-blue-50 rounded-lg">
            <Eye className="h-8 w-8 text-blue-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">透明度</h3>
            <p className="text-sm text-gray-600">清楚说明我们收集和使用数据的方式</p>
          </div>
          <div className="text-center p-6 bg-green-50 rounded-lg">
            <Lock className="h-8 w-8 text-green-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">安全性</h3>
            <p className="text-sm text-gray-600">采用先进技术保护您的个人信息</p>
          </div>
          <div className="text-center p-6 bg-purple-50 rounded-lg">
            <Users className="h-8 w-8 text-purple-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">控制权</h3>
            <p className="text-sm text-gray-600">您可以随时管理和删除您的数据</p>
          </div>
        </div>

        {/* Policy Content */}
        <div className="prose prose-lg max-w-none">
          <h2>1. 信息收集</h2>
          <h3>1.1 我们收集的信息类型</h3>
          <ul>
            <li><strong>账户信息：</strong>当您注册账户时，我们收集您的邮箱地址、用户名等基本信息</li>
            <li><strong>使用数据：</strong>我们收集您在平台上的浏览记录、搜索历史、点赞和评论等行为数据</li>
            <li><strong>设备信息：</strong>包括IP地址、浏览器类型、操作系统等技术信息</li>
            <li><strong>Cookie数据：</strong>用于改善用户体验和网站功能的Cookie信息</li>
          </ul>

          <h3>1.2 信息收集方式</h3>
          <ul>
            <li>您主动提供的信息（注册、提交工具、联系我们等）</li>
            <li>自动收集的信息（网站访问、使用行为等）</li>
            <li>第三方服务提供的信息（OAuth登录等）</li>
          </ul>

          <h2>2. 信息使用</h2>
          <h3>2.1 使用目的</h3>
          <ul>
            <li><strong>服务提供：</strong>为您提供个性化的AI工具推荐和相关服务</li>
            <li><strong>账户管理：</strong>创建和管理您的用户账户</li>
            <li><strong>沟通联系：</strong>发送重要通知、回复您的咨询</li>
            <li><strong>服务改进：</strong>分析使用数据以改进我们的服务质量</li>
            <li><strong>安全保护：</strong>检测和防止欺诈、滥用等安全问题</li>
          </ul>

          <h2>3. 信息分享</h2>
          <h3>3.1 我们不会出售您的个人信息</h3>
          <p>我们承诺不会向第三方出售、出租或交易您的个人信息。</p>

          <h3>3.2 有限的信息分享</h3>
          <p>在以下情况下，我们可能会分享您的信息：</p>
          <ul>
            <li>获得您的明确同意</li>
            <li>法律法规要求</li>
            <li>保护我们或他人的合法权益</li>
            <li>与可信的服务提供商合作（如云存储、数据分析等）</li>
          </ul>

          <h2>4. 数据安全</h2>
          <h3>4.1 安全措施</h3>
          <ul>
            <li><strong>加密传输：</strong>使用SSL/TLS加密保护数据传输</li>
            <li><strong>访问控制：</strong>严格限制员工对个人数据的访问权限</li>
            <li><strong>定期审计：</strong>定期进行安全审计和漏洞检测</li>
            <li><strong>数据备份：</strong>建立完善的数据备份和恢复机制</li>
          </ul>

          <h2>5. 您的权利</h2>
          <h3>5.1 数据控制权</h3>
          <p>您对自己的个人数据享有以下权利：</p>
          <ul>
            <li><strong>访问权：</strong>查看我们收集的关于您的个人信息</li>
            <li><strong>更正权：</strong>更新或修正不准确的个人信息</li>
            <li><strong>删除权：</strong>要求删除您的个人信息</li>
            <li><strong>限制权：</strong>限制我们处理您的个人信息</li>
            <li><strong>反对权：</strong>反对我们处理您的个人信息</li>
          </ul>

          <h2>6. Cookie政策</h2>
          <h3>6.1 Cookie使用</h3>
          <p>我们使用Cookie来：</p>
          <ul>
            <li>记住您的登录状态和偏好设置</li>
            <li>分析网站流量和用户行为</li>
            <li>提供个性化内容和广告</li>
            <li>改善网站性能和用户体验</li>
          </ul>

          <h2>7. 第三方服务</h2>
          <h3>7.1 集成服务</h3>
          <p>我们的网站可能集成以下第三方服务：</p>
          <ul>
            <li>Google Analytics（网站分析）</li>
            <li>Google OAuth（登录服务）</li>
            <li>GitHub OAuth（登录服务）</li>
            <li>Stripe（支付处理）</li>
          </ul>

          <h2>8. 数据保留</h2>
          <h3>8.1 保留期限</h3>
          <ul>
            <li><strong>账户数据：</strong>在您的账户存续期间保留</li>
            <li><strong>使用数据：</strong>通常保留2年，用于服务改进</li>
            <li><strong>日志数据：</strong>通常保留1年，用于安全和故障排除</li>
          </ul>

          <h2>9. 政策更新</h2>
          <h3>9.1 更新通知</h3>
          <p>我们可能会不时更新本隐私政策。重大变更时，我们会通过以下方式通知您：</p>
          <ul>
            <li>在网站上发布显著通知</li>
            <li>向您的注册邮箱发送通知</li>
            <li>在您下次登录时显示更新提醒</li>
          </ul>

          <h2>10. 联系我们</h2>
          <p>如果您对本隐私政策有任何疑问或需要行使您的权利，请通过以下方式联系我们：</p>
          <ul>
            <li>邮箱：<EMAIL></li>
            <li>联系表单：<a href="/contact">联系我们页面</a></li>
          </ul>
        </div>

        {/* Footer */}
        <div className="mt-12 p-6 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-600 text-center">
            本隐私政策是我们与您之间关于个人信息处理的重要协议。
            使用我们的服务即表示您同意本政策的条款。
          </p>
        </div>
      </div>
    </Layout>
  );
}
