import React from 'react';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Layout from '@/components/Layout';
import CategoryPageClient from '@/components/categories/CategoryPageClient';
import { apiClient } from '@/lib/api';
import { getCategoryConfig } from '@/constants/categories';
import { getToolListStructuredData, getBreadcrumbStructuredData } from '@/lib/seo/structuredData';

// 分类信息接口
interface CategoryInfo {
  _id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  toolCount: number;
}

interface CategoryPageProps {
  params: Promise<{
    slug: string;
  }>;
}

// 根据slug生成分类信息的辅助函数
const getCategoryInfoBySlug = (slug: string, toolCount: number): CategoryInfo => {
  const categoryConfig = getCategoryConfig(slug);

  if (categoryConfig) {
    return {
      _id: slug,
      slug,
      name: categoryConfig.name,
      description: categoryConfig.description,
      icon: categoryConfig.icon,
      color: categoryConfig.color,
      toolCount
    };
  }

  // 如果没有找到配置，使用默认值
  return {
    _id: slug,
    slug,
    name: slug.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
    description: `AI tools in the ${slug} category.`,
    icon: '🤖',
    color: '#6B7280',
    toolCount
  };
};

// 生成动态metadata
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  try {
    const resolvedParams = await params;
    const { categoryInfo, tools } = await getCategoryData(resolvedParams.slug);

    if (!categoryInfo) {
      return {
        title: '分类不存在 - AI工具导航',
        description: '您访问的AI工具分类不存在。',
      };
    }

    const title = `${categoryInfo.name} AI工具 - AI工具导航`;
    const description = `发现最好的${categoryInfo.name} AI工具。${categoryInfo.description}，共${tools.length}个精选工具。`;
    const keywords = `${categoryInfo.name},${categoryInfo.name}AI工具,人工智能,AI应用,机器学习,深度学习`;
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com';
    const canonical = `/categories/${categoryInfo.slug}`;

    return {
      title,
      description,
      keywords,
      authors: [{ name: 'AI工具导航团队' }],
      robots: {
        index: true,
        follow: true,
      },
      openGraph: {
        type: 'website',
        locale: 'zh_CN',
        url: `${baseUrl}${canonical}`,
        siteName: 'AI工具导航',
        title,
        description,
        images: [
          {
            url: `/og-category-${categoryInfo.slug}.jpg`,
            width: 1200,
            height: 630,
            alt: `${categoryInfo.name} AI工具`,
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        images: [`/og-category-${categoryInfo.slug}.jpg`],
      },
      alternates: {
        canonical: `${baseUrl}${canonical}`,
      },
    };
  } catch (error: unknown) {
    return {
      title: 'AI工具分类 - AI工具导航',
      description: '浏览AI工具分类，发现适合您需求的人工智能工具。',
    };
  }
}

// 服务端数据获取函数
async function getCategoryData(slug: string) {
  try {
    // 获取该分类下的工具
    const response = await apiClient.getTools({
      category: slug,
      status: 'published',
      limit: 100
    });

    if (response.success && response.data) {
      const tools = response.data.tools;
      // 根据分类slug生成分类信息
      const categoryInfo = getCategoryInfoBySlug(slug, tools.length);

      return {
        categoryInfo,
        tools,
        error: null
      };
    } else {
      return {
        categoryInfo: null,
        tools: [],
        error: response.error || '获取分类数据失败'
      };
    }
  } catch (error) {
    console.error('Failed to fetch category data:', error);
    return {
      categoryInfo: null,
      tools: [],
      error: '获取分类数据失败，请稍后重试'
    };
  }
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const resolvedParams = await params;
  const { categoryInfo, tools, error } = await getCategoryData(resolvedParams.slug);

  // 如果分类不存在，返回404
  if (!categoryInfo) {
    notFound();
  }

  // 生成结构化数据
  const toolListStructuredData = tools.length > 0 ? getToolListStructuredData(tools, categoryInfo.name) : null;
  const breadcrumbStructuredData = getBreadcrumbStructuredData([
    { name: '首页', url: '/' },
    { name: 'AI工具分类', url: '/categories' },
    { name: categoryInfo.name, url: `/categories/${categoryInfo.slug}` }
  ]);

  // 生成分类结构化数据
  const categoryStructuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": `${categoryInfo.name} AI工具`,
    "description": categoryInfo.description,
    "url": `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/categories/${categoryInfo.slug}`,
    "mainEntity": {
      "@type": "ItemList",
      "name": `${categoryInfo.name} AI工具列表`,
      "numberOfItems": tools.length,
      "itemListElement": tools.map((tool, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "SoftwareApplication",
          "name": tool.name,
          "description": tool.description,
          "url": `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/tools/${tool._id}`,
          "applicationCategory": categoryInfo.name,
          "image": tool.logo
        }
      }))
    }
  };

  return (
    <Layout>
      {/* 结构化数据 */}
      {toolListStructuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(toolListStructuredData)
          }}
        />
      )}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbStructuredData)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(categoryStructuredData)
        }}
      />

      {/* 面包屑导航 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav className="flex" aria-label="面包屑导航">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <a
                href="/"
                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
              >
                首页
              </a>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <a
                  href="/categories"
                  className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2"
                >
                  AI工具分类
                </a>
              </div>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">{categoryInfo.name}</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <CategoryPageClient
        categoryInfo={categoryInfo}
        tools={tools}
        error={error}
      />
    </Layout>
  );
}
