import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';
import "../globals.css";
import SessionProvider from "@/components/providers/SessionProvider";
import Header from "@/components/layout/Header";
import { LikeProvider } from "@/contexts/LikeContext";
import { locales, type Locale } from '@/i18n/config';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

type Props = {
  children: React.ReactNode;
  params: { locale: string };
};

export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  
  if (locale === 'zh') {
    return {
      title: "AI工具导航 - 发现最好的人工智能工具",
      description: "探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。",
      keywords: "AI工具,人工智能,AI导航,机器学习工具,深度学习,自动化工具,AI应用",
      authors: [{ name: "AI工具导航团队" }],
      creator: "AI工具导航",
      publisher: "AI工具导航",
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      openGraph: {
        type: 'website',
        locale: 'zh_CN',
        url: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',
        siteName: 'AI工具导航',
        title: 'AI工具导航 - 发现最好的人工智能工具',
        description: '探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。',
        images: [
          {
            url: '/og-image.jpg',
            width: 1200,
            height: 630,
            alt: 'AI工具导航 - 发现最好的人工智能工具',
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title: 'AI工具导航 - 发现最好的人工智能工具',
        description: '探索精选的人工智能工具集合，提升您的工作效率和创造力。',
        images: ['/og-image.jpg'],
      },
      alternates: {
        canonical: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',
        languages: {
          'zh': '/',
          'en': '/en',
        },
      },
      verification: {
        google: 'your-google-verification-code',
      },
    };
  } else {
    return {
      title: "AI Tools Directory - Discover the Best AI Tools",
      description: "Explore curated collection of AI tools to boost your productivity and creativity. From text generation to image creation, find the perfect AI tool for your needs.",
      keywords: "AI tools,artificial intelligence,AI directory,machine learning tools,deep learning,automation tools,AI applications",
      authors: [{ name: "AI Tools Directory Team" }],
      creator: "AI Tools Directory",
      publisher: "AI Tools Directory",
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      openGraph: {
        type: 'website',
        locale: 'en_US',
        url: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',
        siteName: 'AI Tools Directory',
        title: 'AI Tools Directory - Discover the Best AI Tools',
        description: 'Explore curated collection of AI tools to boost your productivity and creativity. From text generation to image creation, find the perfect AI tool for your needs.',
        images: [
          {
            url: '/og-image.jpg',
            width: 1200,
            height: 630,
            alt: 'AI Tools Directory - Discover the Best AI Tools',
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title: 'AI Tools Directory - Discover the Best AI Tools',
        description: 'Explore curated collection of AI tools to boost your productivity and creativity.',
        images: ['/og-image.jpg'],
      },
      alternates: {
        canonical: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',
        languages: {
          'zh': '/',
          'en': '/en',
        },
      },
      verification: {
        google: 'your-google-verification-code',
      },
    };
  }
}

export default async function LocaleLayout({
  children,
  params
}: Props) {
  const { locale } = await params;

  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  // Providing all messages to the client side is the easiest way to get started
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      <SessionProvider>
        <LikeProvider>
          <Header />
          <main className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
            {children}
          </main>
        </LikeProvider>
      </SessionProvider>
    </NextIntlClientProvider>
  );
}
