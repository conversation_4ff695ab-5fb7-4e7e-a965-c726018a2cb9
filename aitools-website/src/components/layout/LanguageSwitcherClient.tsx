'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { FaGlobe } from 'react-icons/fa';
import { locales, localeNames, type Locale } from '@/i18n/config';

interface LanguageSwitcherClientProps {
  currentLocale: Locale;
}

export default function LanguageSwitcherClient({ currentLocale }: LanguageSwitcherClientProps) {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [isOpen]);

  const switchLanguage = (newLocale: Locale) => {
    // Get the current path without the locale prefix
    let pathWithoutLocale = pathname;

    // Remove the current locale prefix if it exists
    for (const locale of locales) {
      if (pathname.startsWith(`/${locale}`)) {
        pathWithoutLocale = pathname.slice(`/${locale}`.length);
        break;
      }
    }

    // Ensure path starts with /
    if (!pathWithoutLocale.startsWith('/')) {
      pathWithoutLocale = '/' + pathWithoutLocale;
    }

    // If path is empty, default to root
    if (pathWithoutLocale === '/') {
      pathWithoutLocale = '';
    }

    // Construct the new path with the new locale
    const newPath = `/${newLocale}${pathWithoutLocale}`;

    // Navigate to the new path
    router.push(newPath);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 px-3 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
      >
        <FaGlobe className="w-4 h-4" />
        <span className="text-sm">{localeNames[currentLocale]}</span>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-[100]">
          {locales.map((locale) => (
            <button
              key={locale}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                switchLanguage(locale);
              }}
              className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${
                locale === currentLocale ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
              }`}
            >
              {localeNames[locale]}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
