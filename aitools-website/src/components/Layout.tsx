'use client';

import React from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import PerformanceMonitor from '@/components/seo/PerformanceMonitor';
import { Locale } from '@/i18n/config';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const params = useParams();
  const locale = params?.locale as Locale || 'zh';
  const t = useTranslations('layout');

  return (
    <div className="min-h-screen bg-gray-50">
      <PerformanceMonitor />

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">AI</span>
                </div>
                <span className="text-xl font-bold text-gray-900">AI Tools</span>
              </div>
              <p className="text-gray-600 mb-4">
                {t('footer.description')}
              </p>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                {t('footer.quick_links')}
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link href={`/${locale}/tools`} className="text-gray-600 hover:text-blue-600">
                    {t('footer.tools_directory')}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/categories`} className="text-gray-600 hover:text-blue-600">
                    {t('footer.browse_categories')}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/submit`} className="text-gray-600 hover:text-blue-600">
                    {t('footer.submit_tool')}
                  </Link>
                </li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                {t('footer.support')}
              </h3>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-gray-600 hover:text-blue-600">
                    {t('footer.help_center')}
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-600 hover:text-blue-600">
                    {t('footer.contact_us')}
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-600 hover:text-blue-600">
                    {t('footer.privacy_policy')}
                  </a>
                </li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-200 mt-8 pt-8">
            <p className="text-center text-gray-600">
              {t('footer.copyright')}
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
